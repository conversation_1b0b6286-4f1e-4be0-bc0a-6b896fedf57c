# Plano para Extensão do Facebook

## Visão Geral
Desenvolveremos uma extensão para Chrome que modifica a interface do Facebook para:
1. Ocultar os stories de outras pessoas
2. Remover sugestões de amizade

## Estrutura de Arquivos
```
extensao-facebook/
├── manifest.json       # Configuração da extensão
├── content.js         # Script que será injetado na página
├── background.js      # Script em background (se necessário)
├── styles.css        # Estilos CSS para ocultar elementos
└── icons/            # Ícones da extensão
    ├── icon16.png
    ├── icon48.png
    └── icon128.png
```

## Funcionamento Técnico

```mermaid
flowchart TD
    A[Usuário instala extensão] --> B[Extension carrega no Chrome]
    B --> C[content.js é injetado na página do Facebook]
    C --> D{Monitora DOM}
    D --> E[Identifica containers de stories]
    D --> F[Identifica sugestões de amizade]
    E --> G[Aplica CSS para ocultar stories]
    F --> H[Aplica CSS para ocultar sugestões]
```

## Detalhes de Implementação

1. **manifest.json**
   - Configuração da extensão
   - Permissões necessárias
   - Definição dos scripts a serem injetados

2. **content.js**
   - Identificar seletores CSS dos elementos a serem ocultados
   - Usar MutationObserver para garantir que elementos sejam ocultados mesmo após mudanças dinâmicas na página
   - Injetar estilos CSS dinamicamente

3. **styles.css**
   - Regras CSS para ocultar os elementos específicos
   - Garantir que não afete outras partes da interface

4. **Compatibilidade**
   - Garantir funcionamento em diferentes resoluções
   - Manter compatibilidade com atualizações do Facebook