/**
 * Facebook Cleaner Extension
 * Observa mudanças no DOM e oculta stories, reels, blocos específicos e remove fotos das sugestões de amizade no Facebook.
 * Versão 1.1.0 com melhorias de performance e tratamento de erros.
 */

// Configurações da extensão
const CONFIG = {
    debounceDelay: 300,
    maxRetries: 3,
    logEnabled: true
};

// Estado da extensão
let isEnabled = true;
let stats = {
    storiesOcultados: 0,
    reelsOcultados: 0,
    fotosRemovidas: 0
};

/**
 * Sistema de logging
 */
function log(message, type = 'info') {
    if (!CONFIG.logEnabled) return;
    const timestamp = new Date().toISOString();
    console.log(`[Facebook Cleaner ${type.toUpperCase()}] ${timestamp}: ${message}`);
}

/**
 * Função debounce para otimizar performance
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Oculta os stories do Facebook com tratamento de erros
 */
function ocultarStories() {
    if (!isEnabled) return;

    try {
        const seletoresStories = [
            'div[role="region"][aria-label*="Stories"]',
            'div[aria-label*="Stories"]',
            'div[data-pagelet*="Stories"]',
            'div[data-pagelet*="StoriesTray"]',
            'div[data-testid*="stories"]'
        ];

        let ocultados = 0;
        seletoresStories.forEach(seletor => {
            const elementos = document.querySelectorAll(seletor);
            elementos.forEach(el => {
                if (el.style.display !== 'none') {
                    el.style.display = 'none';
                    el.setAttribute('data-fb-cleaner', 'stories-hidden');
                    ocultados++;
                }
            });
        });

        if (ocultados > 0) {
            stats.storiesOcultados += ocultados;
            log(`${ocultados} stories ocultados`);
        }
    } catch (error) {
        log(`Erro ao ocultar stories: ${error.message}`, 'error');
    }
}

/**
 * Oculta os Reels do Facebook e blocos relacionados com tratamento de erros
 */
function ocultarReels() {
    if (!isEnabled) return;

    try {
        const seletoresReels = [
            'div[aria-label*="Reels"]',
            'div[data-pagelet*="Reels"]',
            'a[aria-label="reel"]',
            'a[href*="/reel/"]',
            'div.xb57i2i.x1q594ok.x5lxg6s',
            'div[data-testid*="reels"]'
        ];

        let ocultados = 0;
        seletoresReels.forEach(seletor => {
            const elementos = document.querySelectorAll(seletor);
            elementos.forEach(el => {
                if (el.style.display !== 'none') {
                    el.style.display = 'none';
                    el.setAttribute('data-fb-cleaner', 'reels-hidden');
                    ocultados++;
                }
            });
        });

        // Oculta qualquer container com texto "Reels" (mais eficiente)
        const containers = document.querySelectorAll('div, section, aside');
        containers.forEach(el => {
            if (el.innerText && el.innerText.includes('Reels') && el.style.display !== 'none') {
                el.style.display = 'none';
                el.setAttribute('data-fb-cleaner', 'reels-text-hidden');
                ocultados++;
            }
        });

        if (ocultados > 0) {
            stats.reelsOcultados += ocultados;
            log(`${ocultados} reels ocultados`);
        }
    } catch (error) {
        log(`Erro ao ocultar reels: ${error.message}`, 'error');
    }
}

/**
 * Remove fotos das sugestões de amizade e pessoas que talvez conheças com tratamento de erros
 */
function removerFotosSugestoes() {
    if (!isEnabled) return;

    try {
        const seletoresSugestoes = [
            'div[aria-label*="Sugestões de amizade"]',
            'div[aria-label*="Pessoas que talvez conheças"]',
            'div[data-pagelet*="RightRail"]'
        ];

        let fotosRemovidas = 0;

        // Método mais eficiente usando seletores específicos
        seletoresSugestoes.forEach(seletor => {
            const containers = document.querySelectorAll(seletor);
            containers.forEach(container => {
                const imgs = container.querySelectorAll('img');
                imgs.forEach(img => {
                    if (img.style.display !== 'none') {
                        img.style.display = 'none';
                        img.style.visibility = 'hidden';
                        img.setAttribute('data-fb-cleaner', 'suggestion-photo-hidden');
                        fotosRemovidas++;
                    }
                });
            });
        });

        // Fallback para texto específico
        document.querySelectorAll('div, section, aside').forEach(container => {
            if (container.innerText && container.innerText.includes('Pessoas que talvez conheças')) {
                container.querySelectorAll('img').forEach(img => {
                    if (img.style.display !== 'none') {
                        img.style.display = 'none';
                        img.style.visibility = 'hidden';
                        img.setAttribute('data-fb-cleaner', 'suggestion-photo-hidden');
                        fotosRemovidas++;
                    }
                });
            }
        });

        if (fotosRemovidas > 0) {
            stats.fotosRemovidas += fotosRemovidas;
            log(`${fotosRemovidas} fotos de sugestões removidas`);
        }
    } catch (error) {
        log(`Erro ao remover fotos de sugestões: ${error.message}`, 'error');
    }
}

/**
 * Função principal que executa todas as limpezas
 */
const executarLimpeza = debounce(() => {
    if (!isEnabled) return;

    log('Executando limpeza do Facebook...');
    ocultarStories();
    ocultarReels();
    removerFotosSugestoes();
}, CONFIG.debounceDelay);

/**
 * Inicializa o MutationObserver para monitorar mudanças no DOM
 */
function iniciarObservador() {
    try {
        const observer = new MutationObserver((mutations) => {
            // Verifica se houve mudanças relevantes
            const hasRelevantChanges = mutations.some(mutation =>
                mutation.type === 'childList' && mutation.addedNodes.length > 0
            );

            if (hasRelevantChanges) {
                executarLimpeza();
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: false,
            characterData: false
        });

        log('Observer iniciado com sucesso');
        return observer;
    } catch (error) {
        log(`Erro ao iniciar observer: ${error.message}`, 'error');
        return null;
    }
}

/**
 * Carrega configurações do storage
 */
async function carregarConfiguracoes() {
    try {
        const result = await chrome.storage.sync.get(['isEnabled', 'logEnabled']);
        isEnabled = result.isEnabled !== false; // default true
        CONFIG.logEnabled = result.logEnabled !== false; // default true
        log('Configurações carregadas');
    } catch (error) {
        log(`Erro ao carregar configurações: ${error.message}`, 'error');
    }
}

/**
 * Salva estatísticas no storage
 */
async function salvarEstatisticas() {
    try {
        await chrome.storage.local.set({ stats });
        log('Estatísticas salvas');
    } catch (error) {
        log(`Erro ao salvar estatísticas: ${error.message}`, 'error');
    }
}

/**
 * Inicialização da extensão
 */
async function init() {
    log('Iniciando Facebook Cleaner Extension v1.1.0');

    await carregarConfiguracoes();

    // Execução inicial
    executarLimpeza();

    // Inicia o observer
    const observer = iniciarObservador();

    // Salva estatísticas periodicamente
    setInterval(salvarEstatisticas, 30000); // a cada 30 segundos

    // Listener para mudanças de configuração
    chrome.storage.onChanged.addListener((changes) => {
        if (changes.isEnabled) {
            isEnabled = changes.isEnabled.newValue;
            log(`Extensão ${isEnabled ? 'ativada' : 'desativada'}`);
        }
        if (changes.logEnabled) {
            CONFIG.logEnabled = changes.logEnabled.newValue;
        }
    });

    log('Inicialização concluída');
}

// Inicialização
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}