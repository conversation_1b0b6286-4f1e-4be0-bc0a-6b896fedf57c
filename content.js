/**
 * Observa mudanças no DOM e oculta stories, reels, blocos específicos e remove fotos das sugestões de amizade no Facebook.
 */

/**
 * Oculta os stories do Facebook
 */
function ocultarStories() {
    const seletoresStories = [
        'div[role="region"][aria-label*="Stories"]',
        'div[aria-label*="Stories"]',
        'div[data-pagelet*="Stories"]',
        'div[data-pagelet*="StoriesTray"]'
    ];
    seletoresStories.forEach(seletor => {
        document.querySelectorAll(seletor).forEach(el => {
            el.style.display = 'none';
        });
    });
}

/**
 * Oculta os Reels do Facebook e blocos relacionados
 */
function ocultarReels() {
    const seletoresReels = [
        'div[aria-label*="Reels"]',
        'div[data-pagelet*="Reels"]',
        'a[aria-label="reel"]',
        'a[href*="/reel/"]',
        'div.xb57i2i.x1q594ok.x5lxg6s'
    ];
    seletoresReels.forEach(seletor => {
        document.querySelectorAll(seletor).forEach(el => {
            el.style.display = 'none';
        });
    });

    // Oculta qualquer container com texto "Reels"
    document.querySelectorAll('div, section, aside').forEach(el => {
        if (el.innerText && el.innerText.includes('Reels')) {
            el.style.display = 'none';
        }
    });
}

/**
 * Remove fotos das sugestões de amizade e pessoas que talvez conheças
 */
function removerFotosSugestoes() {
    document.querySelectorAll('div, section, aside').forEach(container => {
        if (container.innerText && container.innerText.includes('Pessoas que talvez conheças')) {
            container.querySelectorAll('img').forEach(img => {
                img.style.display = 'none';
                img.style.visibility = 'hidden';
                img.src = '';
                img.srcset = '';
            });
        }
    });
}

/**
 * Inicializa o MutationObserver para monitorar mudanças no DOM
 */
function iniciarObservador() {
    const observer = new MutationObserver(() => {
        ocultarStories();
        ocultarReels();
        removerFotosSugestoes();
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * Inicialização da extensão
 */
function init() {
    ocultarStories();
    ocultarReels();
    removerFotosSugestoes();
    iniciarObservador();
}

document.addEventListener('DOMContentLoaded', init);