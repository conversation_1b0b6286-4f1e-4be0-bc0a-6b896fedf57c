# Facebook Cleaner Extension v1.1.0

## Descrição
Extensão avançada para Google Chrome que limpa e otimiza sua experiência no Facebook, ocultando:
- 📖 Stories de outras pessoas
- 🎬 Reels e vídeos curtos
- 👥 Fotos de sugestões de amizade
- 🔧 Interface de configuração completa

## ✨ Funcionalidades

### Core Features
- ✅ **Oculta Stories**: Remove todos os stories da timeline
- ✅ **Remove Reels**: Oculta seção de reels e vídeos curtos
- ✅ **Limpa Sugestões**: Remove fotos de pessoas que talvez você conheça
- ✅ **Monitoramento Dinâmico**: Funciona com carregamento AJAX do Facebook
- ✅ **Performance Otimizada**: Sistema de debounce para melhor performance

### Interface e Controle
- 🎛️ **Popup de Configuração**: Interface moderna para controlar a extensão
- 📊 **Estatísticas em Tempo Real**: Veja quantos elementos foram ocultados
- 🔄 **Controles Individuais**: Ative/desative funcionalidades específicas
- 💾 **Configurações Persistentes**: Suas preferências são salvas automaticamente

### Recursos Avançados
- 🐛 **Sistema de Logs**: Debug opcional para desenvolvedores
- 🔒 **Tratamento de Erros**: Código robusto com fallbacks
- 🌐 **Suporte Multi-idioma**: Funciona em Facebook PT, EN, ES
- ⚡ **Otimização CSS**: Seletores eficientes e compatíveis

## Como instalar

1. **Clone ou baixe este repositório para seu computador**

2. **Abra o Google Chrome e acesse:**
```
chrome://extensions/
```

3. **Ative o modo desenvolvedor** (canto superior direito)

4. **Clique em "Carregar sem compactação"**

5. **Selecione a pasta onde estão os arquivos da extensão**

6. **A extensão aparecerá na barra do Chrome**

## Como usar

- Acesse o Facebook normalmente
- Os stories e sugestões de amizade serão ocultados automaticamente
- Caso a página seja atualizada ou novos conteúdos carreguem, a extensão continuará funcionando

## Personalização

- Para alterar os ícones, substitua os arquivos em `icons/` por imagens PNG com tamanhos 16x16, 48x48 e 128x128 pixels.
- Para adicionar ou remover funcionalidades, edite os arquivos `content.js` e `styles.css`.

## Observações

- Esta extensão funciona apenas no domínio `facebook.com`.
- Não coleta nenhum dado do usuário.
- Código aberto para modificações.

---
Desenvolvido por Roo AI