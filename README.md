# Facebook Cleaner Extension

## Descrição
Extensão para Google Chrome que oculta:
- Stories de outras pessoas no Facebook
- Sugestões de amizade

## Funcionalidades
- Remove visualmente os stories e sugestões de amizade
- Funciona mesmo com carregamento dinâmico da página
- Código modular e documentado

## Como instalar

1. **<PERSON><PERSON> ou baixe este repositório para seu computador**

2. **Abra o Google Chrome e acesse:**
```
chrome://extensions/
```

3. **Ative o modo desenvolvedor** (canto superior direito)

4. **Clique em "Carregar sem compactação"**

5. **Selecione a pasta onde estão os arquivos da extensão**

6. **A extensão aparecerá na barra do Chrome**

## Como usar

- Acesse o Facebook normalmente
- Os stories e sugestões de amizade serão ocultados automaticamente
- Caso a página seja atualizada ou novos conteúdos carreguem, a extensão continuará funcionando

## Personalização

- Para alterar os ícones, substitua os arquivos em `icons/` por imagens PNG com tamanhos 16x16, 48x48 e 128x128 pixels.
- Para adicionar ou remover funcionalidades, edite os arquivos `content.js` e `styles.css`.

## Observações

- Esta extensão funciona apenas no domínio `facebook.com`.
- Não coleta nenhum dado do usuário.
- Código aberto para modificações.

---
Desenvolvido por Roo AI