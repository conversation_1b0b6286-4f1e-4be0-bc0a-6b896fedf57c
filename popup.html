<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Cleaner - Configurações</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <img src="icons/icon48.png" alt="Facebook Cleaner" class="logo">
            <h1>Facebook Cleaner</h1>
            <span class="version">v1.1.0</span>
        </header>

        <main class="main">
            <!-- Status da Extensão -->
            <section class="status-section">
                <div class="status-item">
                    <label class="switch">
                        <input type="checkbox" id="enableExtension" checked>
                        <span class="slider"></span>
                    </label>
                    <div class="status-text">
                        <h3>Extensão Ativa</h3>
                        <p id="statusDescription">Ocultando conteúdo indesejado</p>
                    </div>
                </div>
            </section>

            <!-- Configurações -->
            <section class="config-section">
                <h2>Configurações</h2>
                
                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="hideStories" checked>
                        <span class="checkmark"></span>
                        Ocultar Stories
                    </label>
                </div>
                
                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="hideReels" checked>
                        <span class="checkmark"></span>
                        Ocultar Reels
                    </label>
                </div>
                
                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="hideSuggestionPhotos" checked>
                        <span class="checkmark"></span>
                        Remover Fotos de Sugestões
                    </label>
                </div>
                
                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="enableLogging">
                        <span class="checkmark"></span>
                        Logs de Debug
                    </label>
                </div>
            </section>

            <!-- Estatísticas -->
            <section class="stats-section">
                <h2>Estatísticas</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number" id="storiesCount">0</span>
                        <span class="stat-label">Stories Ocultados</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="reelsCount">0</span>
                        <span class="stat-label">Reels Ocultados</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="photosCount">0</span>
                        <span class="stat-label">Fotos Removidas</span>
                    </div>
                </div>
                <button id="resetStats" class="reset-btn">Resetar Estatísticas</button>
            </section>

            <!-- Ações -->
            <section class="actions-section">
                <button id="refreshPage" class="action-btn primary">
                    <span class="icon">🔄</span>
                    Atualizar Página
                </button>
                <button id="openFacebook" class="action-btn secondary">
                    <span class="icon">📘</span>
                    Abrir Facebook
                </button>
            </section>
        </main>

        <footer class="footer">
            <p>Desenvolvido por Roo AI</p>
            <div class="links">
                <a href="#" id="reportIssue">Reportar Problema</a>
                <a href="#" id="rateExtension">Avaliar</a>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
