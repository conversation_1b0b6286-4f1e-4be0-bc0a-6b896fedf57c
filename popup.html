<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Cleaner - Configurações</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <img src="icons/icon48.png" alt="Facebook Cleaner" class="logo">
            <h1>Facebook Cleaner</h1>
            <span class="version">v1.1.0</span>
        </header>

        <main class="main">
            <!-- Status da Extensão -->
            <section class="status-section">
                <div class="status-item">
                    <label class="switch">
                        <input type="checkbox" id="enableExtension" checked>
                        <span class="slider"></span>
                    </label>
                    <div class="status-text">
                        <h3>Extensão Ativa</h3>
                        <p id="statusDescription">Ocultando conteúdo indesejado</p>
                    </div>
                </div>
            </section>

            <!-- Configurações -->
            <section class="config-section">
                <h2>Configurações</h2>

                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="hideStories" checked>
                        <span class="checkmark"></span>
                        Ocultar Stories
                    </label>
                    <span class="shortcut">Ctrl+Shift+S</span>
                </div>

                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="hideReels" checked>
                        <span class="checkmark"></span>
                        Ocultar Reels
                    </label>
                    <span class="shortcut">Ctrl+Shift+R</span>
                </div>

                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="hideSuggestionPhotos" checked>
                        <span class="checkmark"></span>
                        Remover Fotos de Sugestões
                    </label>
                </div>

                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="enableLogging">
                        <span class="checkmark"></span>
                        Logs de Debug
                    </label>
                </div>

                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="darkMode">
                        <span class="checkmark"></span>
                        Modo Escuro (Interface)
                    </label>
                </div>

                <div class="config-item">
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoUpdate" checked>
                        <span class="checkmark"></span>
                        Atualizações Automáticas
                    </label>
                </div>
            </section>

            <!-- Configurações Avançadas -->
            <section class="advanced-section" id="advancedSection" style="display: none;">
                <h2>Configurações Avançadas</h2>

                <div class="config-item">
                    <label for="whitelistInput">Whitelist de Usuários:</label>
                    <div class="whitelist-container">
                        <input type="text" id="whitelistInput" placeholder="Nome do usuário...">
                        <button id="addToWhitelist" class="add-btn">+</button>
                    </div>
                    <div id="whitelistItems" class="whitelist-items"></div>
                </div>

                <div class="config-item">
                    <label for="customCSS">CSS Personalizado:</label>
                    <textarea id="customCSS" placeholder="/* Adicione seus seletores CSS personalizados aqui */"></textarea>
                </div>
            </section>

            <!-- Toggle para Configurações Avançadas -->
            <div class="advanced-toggle">
                <button id="toggleAdvanced" class="toggle-btn">
                    <span class="icon">⚙️</span>
                    Configurações Avançadas
                    <span class="arrow">▼</span>
                </button>
            </div>

            <!-- Estatísticas -->
            <section class="stats-section">
                <h2>Estatísticas</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number" id="storiesCount">0</span>
                        <span class="stat-label">Stories Ocultados</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="reelsCount">0</span>
                        <span class="stat-label">Reels Ocultados</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="photosCount">0</span>
                        <span class="stat-label">Fotos Removidas</span>
                    </div>
                </div>
                <button id="resetStats" class="reset-btn">Resetar Estatísticas</button>
            </section>

            <!-- Ações -->
            <section class="actions-section">
                <button id="refreshPage" class="action-btn primary">
                    <span class="icon">🔄</span>
                    Atualizar Página
                </button>
                <button id="openFacebook" class="action-btn secondary">
                    <span class="icon">📘</span>
                    Abrir Facebook
                </button>
            </section>
        </main>

        <footer class="footer">
            <p>Desenvolvido por Roo AI</p>
            <div class="links">
                <a href="#" id="reportIssue">Reportar Problema</a>
                <a href="#" id="rateExtension">Avaliar</a>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
