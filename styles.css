/* Facebook Cleaner Extension - Enhanced CSS Rules v1.1.0 */

/* ========== STORIES ========== */
/* Oculta os stories do Facebook com seletores robustos */
div[role="region"][aria-label*="Stories"],
div[aria-label*="Stories"],
div[data-pagelet*="Stories"],
div[data-pagelet*="StoriesTray"],
div[data-testid*="stories"],
div[data-testid="story_tray"],
section[aria-label*="Stories"] {
    display: none !important;
    visibility: hidden !important;
}

/* Seletores adicionais para stories em diferentes idiomas */
div[aria-label*="Histórias"],
div[aria-label*="Stories"],
div[aria-label*="Historias"] {
    display: none !important;
    visibility: hidden !important;
}

/* ========== REELS ========== */
/* Oculta Reels com seletores abrangentes */
div[aria-label*="Reels"],
div[data-pagelet*="Reels"],
div[data-testid*="reels"],
a[aria-label*="reel"],
a[href*="/reel/"],
div[role="main"] div:has(span:contains("Reels")),
section[aria-label*="Reels"] {
    display: none !important;
    visibility: hidden !important;
}

/* Oculta containers de reels por texto */
div:has(> div > span:contains("Reels")),
div:has(> span:contains("Reels")) {
    display: none !important;
}

/* ========== SUGESTÕES DE AMIZADE ========== */
/* Oculta fotos em sugestões de amizade de forma mais específica */
div[aria-label*="Sugestões de amizade"] img,
div[aria-label*="Pessoas que talvez conheças"] img,
div[aria-label*="People you may know"] img,
div[data-pagelet*="RightRail"] img,
aside img[src*="profile"],
div[data-testid*="friend_suggestion"] img {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Oculta seções inteiras de sugestões se necessário */
div[aria-label*="Sugestões de amizade"],
div[aria-label*="People you may know"],
div[data-testid*="friend_suggestions"] {
    /* Comentado para manter apenas remoção de fotos */
    /* display: none !important; */
}

/* ========== CLASSES DINÂMICAS ========== */
/* Oculta blocos com classes específicas do Facebook */
div.xb57i2i.x1q594ok.x5lxg6s,
div.x1ey2m1c.x78zum5.xdt5ytf.xozqiw3.x17qophe.x13a6bvl.x10l6tqk.x13vifvy.xq2gx43.xh8yej3 {
    display: none !important;
}

/* ========== ELEMENTOS MARCADOS PELA EXTENSÃO ========== */
/* Garante que elementos marcados pela extensão permaneçam ocultos */
[data-fb-cleaner="stories-hidden"],
[data-fb-cleaner="reels-hidden"],
[data-fb-cleaner="reels-text-hidden"] {
    display: none !important;
    visibility: hidden !important;
}

[data-fb-cleaner="suggestion-photo-hidden"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* ========== PERFORMANCE E COMPATIBILIDADE ========== */
/* Otimizações para melhor performance */
div[style*="display: none"][data-fb-cleaner] {
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
}

/* Previne flash de conteúdo não estilizado */
body:not(.fb-cleaner-loaded) div[role="region"][aria-label*="Stories"] {
    opacity: 0 !important;
}

/* Marca o body quando a extensão carrega */
body.fb-cleaner-loaded {
    /* Extensão carregada */
}