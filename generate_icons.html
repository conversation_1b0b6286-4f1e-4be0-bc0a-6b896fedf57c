<!DOCTYPE html>
<html>
<head>
    <title>Gerador de Ícones - Facebook Cleaner</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-container { display: inline-block; margin: 10px; text-align: center; }
        button { padding: 10px 20px; margin: 5px; background: #4267B2; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #365899; }
    </style>
</head>
<body>
    <h1>Gerador de Ícones - Facebook Cleaner Extension</h1>
    <p>Clique nos botões para gerar e baixar os ícones necessários:</p>
    
    <div class="icon-container">
        <h3>Ícone 16x16</h3>
        <canvas id="icon16" width="16" height="16"></canvas><br>
        <button onclick="generateIcon(16)">Gerar 16x16</button>
        <button onclick="downloadIcon('icon16', 'icon16.png')">Bai<PERSON>r</button>
    </div>
    
    <div class="icon-container">
        <h3>Ícone 48x48</h3>
        <canvas id="icon48" width="48" height="48"></canvas><br>
        <button onclick="generateIcon(48)">Gerar 48x48</button>
        <button onclick="downloadIcon('icon48', 'icon48.png')">Baixar</button>
    </div>
    
    <div class="icon-container">
        <h3>Ícone 128x128 (Referência)</h3>
        <canvas id="icon128" width="128" height="128"></canvas><br>
        <button onclick="generateIcon(128)">Gerar 128x128</button>
        <button onclick="downloadIcon('icon128', 'icon128.png')">Baixar</button>
    </div>

    <script>
        function generateIcon(size) {
            const canvas = document.getElementById(`icon${size}`);
            const ctx = canvas.getContext('2d');
            
            // Limpar canvas
            ctx.clearRect(0, 0, size, size);
            
            // Fundo azul do Facebook
            ctx.fillStyle = '#4267B2';
            ctx.fillRect(0, 0, size, size);
            
            // Símbolo de "proibido" ou "oculto"
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.35;
            
            // Círculo branco
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Linha diagonal (símbolo de "não")
            ctx.strokeStyle = '#FF0000';
            ctx.lineWidth = size * 0.08;
            ctx.beginPath();
            ctx.moveTo(centerX - radius * 0.7, centerY - radius * 0.7);
            ctx.lineTo(centerX + radius * 0.7, centerY + radius * 0.7);
            ctx.stroke();
            
            // Adicionar "F" pequeno se o ícone for grande o suficiente
            if (size >= 48) {
                ctx.fillStyle = '#4267B2';
                ctx.font = `bold ${size * 0.3}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('F', centerX, centerY - size * 0.05);
            }
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Gerar ícones automaticamente ao carregar
        window.onload = function() {
            generateIcon(16);
            generateIcon(48);
            generateIcon(128);
        };
    </script>
</body>
</html>
