/* Facebook Cleaner - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    width: 350px;
    min-height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

/* Header */
.header {
    background: #4267B2;
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.logo {
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.version {
    font-size: 12px;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 10px;
}

/* Main Content */
.main {
    padding: 20px;
}

/* Status Section */
.status-section {
    margin-bottom: 24px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.status-item.disabled {
    border-left-color: #dc3545;
}

.status-text h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.status-text p {
    font-size: 12px;
    color: #666;
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4267B2;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Config Section */
.config-section {
    margin-bottom: 24px;
}

.config-section h2 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
}

.config-item {
    margin-bottom: 12px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background-color: #eee;
    border-radius: 3px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s;
}

.checkbox-label input:checked + .checkmark {
    background-color: #4267B2;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 6px;
    top: 3px;
    width: 5px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label input:checked + .checkmark:after {
    display: block;
}

/* Stats Section */
.stats-section {
    margin-bottom: 24px;
}

.stats-section h2 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
}

.stat-item {
    text-align: center;
    padding: 12px 8px;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat-number {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #4267B2;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

.reset-btn {
    width: 100%;
    padding: 8px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.reset-btn:hover {
    background: #5a6268;
}

/* Actions Section */
.actions-section {
    margin-bottom: 20px;
}

.action-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.action-btn.primary {
    background: #4267B2;
    color: white;
}

.action-btn.primary:hover {
    background: #365899;
}

.action-btn.secondary {
    background: #e9ecef;
    color: #495057;
}

.action-btn.secondary:hover {
    background: #dee2e6;
}

.icon {
    font-size: 16px;
}

/* Footer */
.footer {
    background: #f8f9fa;
    padding: 16px 20px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.footer p {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.links {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.links a {
    font-size: 11px;
    color: #4267B2;
    text-decoration: none;
}

.links a:hover {
    text-decoration: underline;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.container {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .container {
        width: 320px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}
