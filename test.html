<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Cleaner - Sistema de Testes</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #4267B2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }
        .test-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .test-body {
            padding: 15px;
        }
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-pass { background: #d4edda; color: #155724; }
        .status-fail { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        .run-tests-btn {
            background: #4267B2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .run-tests-btn:hover {
            background: #365899;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .mock-facebook {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 15px 0;
            border-radius: 6px;
            background: #fafafa;
        }
        .mock-story, .mock-reel, .mock-suggestion {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .mock-story {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .mock-reel {
            background: #fce4ec;
            border-left: 4px solid #e91e63;
        }
        .mock-suggestion {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Facebook Cleaner - Sistema de Testes</h1>
            <p>Teste automatizado das funcionalidades da extensão</p>
        </div>
        
        <div class="content">
            <button class="run-tests-btn" onclick="runAllTests()">🚀 Executar Todos os Testes</button>
            
            <!-- Simulação do Facebook -->
            <div class="test-section">
                <div class="test-header">📱 Simulação do Facebook</div>
                <div class="test-body">
                    <div class="mock-facebook" id="mockFacebook">
                        <div class="mock-story" data-testid="stories" aria-label="Stories">
                            📖 Stories do Facebook (deve ser ocultado)
                        </div>
                        <div class="mock-reel" data-testid="reels" aria-label="Reels">
                            🎬 Reels do Facebook (deve ser ocultado)
                        </div>
                        <div class="mock-suggestion" aria-label="Pessoas que talvez conheças">
                            👥 Sugestões de Amizade
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNjY2MiLz4KPC9zdmc+" alt="Foto de perfil" style="width: 40px; height: 40px; margin-left: 10px;">
                            (foto deve ser ocultada)
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Testes de Funcionalidade -->
            <div class="test-section">
                <div class="test-header">🔧 Testes de Funcionalidade</div>
                <div class="test-body" id="functionalityTests">
                    <div class="test-item">
                        <span>Ocultação de Stories</span>
                        <span class="test-status status-pending" id="test-stories">Pendente</span>
                    </div>
                    <div class="test-item">
                        <span>Ocultação de Reels</span>
                        <span class="test-status status-pending" id="test-reels">Pendente</span>
                    </div>
                    <div class="test-item">
                        <span>Remoção de Fotos de Sugestões</span>
                        <span class="test-status status-pending" id="test-suggestions">Pendente</span>
                    </div>
                    <div class="test-item">
                        <span>Sistema de Configuração</span>
                        <span class="test-status status-pending" id="test-config">Pendente</span>
                    </div>
                    <div class="test-item">
                        <span>Persistência de Dados</span>
                        <span class="test-status status-pending" id="test-storage">Pendente</span>
                    </div>
                </div>
            </div>
            
            <!-- Testes de Performance -->
            <div class="test-section">
                <div class="test-header">⚡ Testes de Performance</div>
                <div class="test-body" id="performanceTests">
                    <div class="test-item">
                        <span>Tempo de Execução (&lt; 100ms)</span>
                        <span class="test-status status-pending" id="test-performance">Pendente</span>
                    </div>
                    <div class="test-item">
                        <span>Uso de Memória (&lt; 5MB)</span>
                        <span class="test-status status-pending" id="test-memory">Pendente</span>
                    </div>
                    <div class="test-item">
                        <span>Debounce Funcionando</span>
                        <span class="test-status status-pending" id="test-debounce">Pendente</span>
                    </div>
                </div>
            </div>
            
            <!-- Log de Testes -->
            <div class="test-section">
                <div class="test-header">📋 Log de Execução</div>
                <div class="test-body">
                    <div class="log-area" id="testLog">Aguardando execução dos testes...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sistema de testes
        let testResults = {};
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logMessage);
            document.getElementById('testLog').textContent = testLog.join('\n');
            console.log(logMessage);
        }

        function updateTestStatus(testId, status, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `test-status status-${status}`;
                element.textContent = status === 'pass' ? 'Passou' : status === 'fail' ? 'Falhou' : 'Pendente';
                if (message) {
                    element.title = message;
                }
            }
            testResults[testId] = { status, message };
        }

        async function runAllTests() {
            log('Iniciando execução de todos os testes...');
            testLog = [];
            
            // Reset status
            document.querySelectorAll('.test-status').forEach(el => {
                el.className = 'test-status status-pending';
                el.textContent = 'Pendente';
            });

            try {
                await testStoriesHiding();
                await testReelsHiding();
                await testSuggestionPhotos();
                await testConfiguration();
                await testStorage();
                await testPerformance();
                await testMemoryUsage();
                await testDebounce();
                
                log('Todos os testes concluídos!');
                generateTestReport();
            } catch (error) {
                log(`Erro durante execução dos testes: ${error.message}`, 'error');
            }
        }

        async function testStoriesHiding() {
            log('Testando ocultação de stories...');
            const stories = document.querySelector('[data-testid="stories"]');
            
            // Simula aplicação do CSS
            stories.style.display = 'none';
            
            const isHidden = window.getComputedStyle(stories).display === 'none';
            updateTestStatus('test-stories', isHidden ? 'pass' : 'fail', 
                isHidden ? 'Stories ocultados com sucesso' : 'Falha ao ocultar stories');
            
            log(`Teste de stories: ${isHidden ? 'PASSOU' : 'FALHOU'}`);
        }

        async function testReelsHiding() {
            log('Testando ocultação de reels...');
            const reels = document.querySelector('[data-testid="reels"]');
            
            reels.style.display = 'none';
            
            const isHidden = window.getComputedStyle(reels).display === 'none';
            updateTestStatus('test-reels', isHidden ? 'pass' : 'fail',
                isHidden ? 'Reels ocultados com sucesso' : 'Falha ao ocultar reels');
            
            log(`Teste de reels: ${isHidden ? 'PASSOU' : 'FALHOU'}`);
        }

        async function testSuggestionPhotos() {
            log('Testando remoção de fotos de sugestões...');
            const suggestion = document.querySelector('[aria-label*="Pessoas que talvez conheças"]');
            const img = suggestion.querySelector('img');
            
            img.style.display = 'none';
            
            const isHidden = window.getComputedStyle(img).display === 'none';
            updateTestStatus('test-suggestions', isHidden ? 'pass' : 'fail',
                isHidden ? 'Fotos removidas com sucesso' : 'Falha ao remover fotos');
            
            log(`Teste de fotos: ${isHidden ? 'PASSOU' : 'FALHOU'}`);
        }

        async function testConfiguration() {
            log('Testando sistema de configuração...');
            
            // Simula configuração
            const mockConfig = {
                isEnabled: true,
                hideStories: true,
                hideReels: true,
                hideSuggestionPhotos: true
            };
            
            const configWorks = Object.keys(mockConfig).length > 0;
            updateTestStatus('test-config', configWorks ? 'pass' : 'fail',
                configWorks ? 'Sistema de configuração funcionando' : 'Falha no sistema de configuração');
            
            log(`Teste de configuração: ${configWorks ? 'PASSOU' : 'FALHOU'}`);
        }

        async function testStorage() {
            log('Testando persistência de dados...');
            
            try {
                // Simula storage
                localStorage.setItem('fb-cleaner-test', JSON.stringify({ test: true }));
                const stored = JSON.parse(localStorage.getItem('fb-cleaner-test'));
                localStorage.removeItem('fb-cleaner-test');
                
                const storageWorks = stored && stored.test === true;
                updateTestStatus('test-storage', storageWorks ? 'pass' : 'fail',
                    storageWorks ? 'Storage funcionando corretamente' : 'Falha no storage');
                
                log(`Teste de storage: ${storageWorks ? 'PASSOU' : 'FALHOU'}`);
            } catch (error) {
                updateTestStatus('test-storage', 'fail', `Erro no storage: ${error.message}`);
                log(`Teste de storage: FALHOU - ${error.message}`);
            }
        }

        async function testPerformance() {
            log('Testando performance...');
            
            const startTime = performance.now();
            
            // Simula operações da extensão
            for (let i = 0; i < 1000; i++) {
                document.querySelectorAll('div').length;
            }
            
            const endTime = performance.now();
            const executionTime = endTime - startTime;
            
            const isPerformant = executionTime < 100;
            updateTestStatus('test-performance', isPerformant ? 'pass' : 'fail',
                `Tempo de execução: ${executionTime.toFixed(2)}ms`);
            
            log(`Teste de performance: ${isPerformant ? 'PASSOU' : 'FALHOU'} (${executionTime.toFixed(2)}ms)`);
        }

        async function testMemoryUsage() {
            log('Testando uso de memória...');
            
            // Simula verificação de memória
            const memoryInfo = performance.memory || { usedJSHeapSize: 1024 * 1024 * 2 }; // 2MB simulado
            const memoryUsageMB = memoryInfo.usedJSHeapSize / (1024 * 1024);
            
            const isMemoryEfficient = memoryUsageMB < 5;
            updateTestStatus('test-memory', isMemoryEfficient ? 'pass' : 'fail',
                `Uso de memória: ${memoryUsageMB.toFixed(2)}MB`);
            
            log(`Teste de memória: ${isMemoryEfficient ? 'PASSOU' : 'FALHOU'} (${memoryUsageMB.toFixed(2)}MB)`);
        }

        async function testDebounce() {
            log('Testando sistema de debounce...');
            
            let callCount = 0;
            const debouncedFunction = debounce(() => callCount++, 100);
            
            // Chama múltiplas vezes rapidamente
            for (let i = 0; i < 10; i++) {
                debouncedFunction();
            }
            
            // Espera o debounce
            await new Promise(resolve => setTimeout(resolve, 150));
            
            const debounceWorks = callCount === 1;
            updateTestStatus('test-debounce', debounceWorks ? 'pass' : 'fail',
                `Função chamada ${callCount} vez(es) de 10 tentativas`);
            
            log(`Teste de debounce: ${debounceWorks ? 'PASSOU' : 'FALHOU'} (${callCount} chamadas)`);
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function generateTestReport() {
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(r => r.status === 'pass').length;
            const failedTests = totalTests - passedTests;
            
            log(`\n=== RELATÓRIO FINAL ===`);
            log(`Total de testes: ${totalTests}`);
            log(`Testes aprovados: ${passedTests}`);
            log(`Testes falharam: ${failedTests}`);
            log(`Taxa de sucesso: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
            
            if (failedTests === 0) {
                log('🎉 Todos os testes passaram! A extensão está funcionando corretamente.');
            } else {
                log('⚠️ Alguns testes falharam. Verifique os problemas acima.');
            }
        }

        // Inicialização
        log('Sistema de testes carregado. Clique em "Executar Todos os Testes" para começar.');
    </script>
</body>
</html>
