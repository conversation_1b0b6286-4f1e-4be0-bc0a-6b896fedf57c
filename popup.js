/**
 * Facebook Cleaner Extension - Popup Script
 * Gerencia a interface de configuração da extensão
 */

// Elementos do DOM
const elements = {
    enableExtension: document.getElementById('enableExtension'),
    hideStories: document.getElementById('hideStories'),
    hideReels: document.getElementById('hideReels'),
    hideSuggestionPhotos: document.getElementById('hideSuggestionPhotos'),
    enableLogging: document.getElementById('enableLogging'),
    darkMode: document.getElementById('darkMode'),
    autoUpdate: document.getElementById('autoUpdate'),
    statusDescription: document.getElementById('statusDescription'),
    storiesCount: document.getElementById('storiesCount'),
    reelsCount: document.getElementById('reelsCount'),
    photosCount: document.getElementById('photosCount'),
    resetStats: document.getElementById('resetStats'),
    refreshPage: document.getElementById('refreshPage'),
    openFacebook: document.getElementById('openFacebook'),
    reportIssue: document.getElementById('reportIssue'),
    rateExtension: document.getElementById('rateExtension'),
    toggleAdvanced: document.getElementById('toggleAdvanced'),
    advancedSection: document.getElementById('advancedSection'),
    whitelistInput: document.getElementById('whitelistInput'),
    addToWhitelist: document.getElementById('addToWhitelist'),
    whitelistItems: document.getElementById('whitelistItems'),
    customCSS: document.getElementById('customCSS')
};

// Estado da aplicação
let currentConfig = {
    isEnabled: true,
    hideStories: true,
    hideReels: true,
    hideSuggestionPhotos: true,
    logEnabled: false,
    darkMode: false,
    autoUpdate: true,
    whitelist: [],
    customCSS: ''
};

let currentStats = {
    storiesOcultados: 0,
    reelsOcultados: 0,
    fotosRemovidas: 0
};

/**
 * Carrega configurações do storage
 */
async function loadConfig() {
    try {
        const result = await chrome.storage.sync.get([
            'isEnabled', 'hideStories', 'hideReels',
            'hideSuggestionPhotos', 'logEnabled', 'darkMode',
            'autoUpdate', 'whitelist', 'customCSS'
        ]);

        currentConfig = {
            isEnabled: result.isEnabled !== false,
            hideStories: result.hideStories !== false,
            hideReels: result.hideReels !== false,
            hideSuggestionPhotos: result.hideSuggestionPhotos !== false,
            logEnabled: result.logEnabled === true,
            darkMode: result.darkMode === true,
            autoUpdate: result.autoUpdate !== false,
            whitelist: result.whitelist || [],
            customCSS: result.customCSS || ''
        };

        updateUI();
        updateWhitelistUI();
        applyDarkMode();
    } catch (error) {
        console.error('Erro ao carregar configurações:', error);
    }
}

/**
 * Carrega estatísticas do storage
 */
async function loadStats() {
    try {
        const result = await chrome.storage.local.get(['stats']);
        if (result.stats) {
            currentStats = result.stats;
            updateStatsUI();
        }
    } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
    }
}

/**
 * Salva configurações no storage
 */
async function saveConfig() {
    try {
        await chrome.storage.sync.set(currentConfig);
        console.log('Configurações salvas');
    } catch (error) {
        console.error('Erro ao salvar configurações:', error);
    }
}

/**
 * Atualiza a interface com as configurações atuais
 */
function updateUI() {
    elements.enableExtension.checked = currentConfig.isEnabled;
    elements.hideStories.checked = currentConfig.hideStories;
    elements.hideReels.checked = currentConfig.hideReels;
    elements.hideSuggestionPhotos.checked = currentConfig.hideSuggestionPhotos;
    elements.enableLogging.checked = currentConfig.logEnabled;
    elements.darkMode.checked = currentConfig.darkMode;
    elements.autoUpdate.checked = currentConfig.autoUpdate;
    elements.customCSS.value = currentConfig.customCSS;

    // Atualiza status
    const statusItem = document.querySelector('.status-item');
    if (currentConfig.isEnabled) {
        statusItem.classList.remove('disabled');
        elements.statusDescription.textContent = 'Ocultando conteúdo indesejado';
    } else {
        statusItem.classList.add('disabled');
        elements.statusDescription.textContent = 'Extensão desativada';
    }

    // Habilita/desabilita controles
    const configInputs = [elements.hideStories, elements.hideReels, elements.hideSuggestionPhotos];
    configInputs.forEach(input => {
        input.disabled = !currentConfig.isEnabled;
        input.parentElement.style.opacity = currentConfig.isEnabled ? '1' : '0.5';
    });
}

/**
 * Aplica modo escuro
 */
function applyDarkMode() {
    if (currentConfig.darkMode) {
        document.body.classList.add('dark-mode');
    } else {
        document.body.classList.remove('dark-mode');
    }
}

/**
 * Atualiza a interface da whitelist
 */
function updateWhitelistUI() {
    elements.whitelistItems.innerHTML = '';

    currentConfig.whitelist.forEach((user, index) => {
        const item = document.createElement('div');
        item.className = 'whitelist-item';
        item.innerHTML = `
            <span>${user}</span>
            <button class="remove-btn" data-index="${index}">×</button>
        `;
        elements.whitelistItems.appendChild(item);
    });

    // Adiciona listeners para botões de remoção
    elements.whitelistItems.querySelectorAll('.remove-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const index = parseInt(e.target.dataset.index);
            removeFromWhitelist(index);
        });
    });
}

/**
 * Adiciona usuário à whitelist
 */
async function addToWhitelist() {
    const username = elements.whitelistInput.value.trim();
    if (username && !currentConfig.whitelist.includes(username)) {
        currentConfig.whitelist.push(username);
        elements.whitelistInput.value = '';
        await saveConfig();
        updateWhitelistUI();
    }
}

/**
 * Remove usuário da whitelist
 */
async function removeFromWhitelist(index) {
    currentConfig.whitelist.splice(index, 1);
    await saveConfig();
    updateWhitelistUI();
}

/**
 * Alterna seção avançada
 */
function toggleAdvancedSection() {
    const isVisible = elements.advancedSection.style.display !== 'none';
    elements.advancedSection.style.display = isVisible ? 'none' : 'block';
    elements.toggleAdvanced.classList.toggle('active', !isVisible);
}

/**
 * Atualiza as estatísticas na interface
 */
function updateStatsUI() {
    elements.storiesCount.textContent = currentStats.storiesOcultados || 0;
    elements.reelsCount.textContent = currentStats.reelsOcultados || 0;
    elements.photosCount.textContent = currentStats.fotosRemovidas || 0;
}

/**
 * Reseta as estatísticas
 */
async function resetStats() {
    try {
        currentStats = {
            storiesOcultados: 0,
            reelsOcultados: 0,
            fotosRemovidas: 0
        };

        await chrome.storage.local.set({ stats: currentStats });
        updateStatsUI();

        // Feedback visual
        elements.resetStats.textContent = 'Resetado!';
        setTimeout(() => {
            elements.resetStats.textContent = 'Resetar Estatísticas';
        }, 1500);
    } catch (error) {
        console.error('Erro ao resetar estatísticas:', error);
    }
}

/**
 * Atualiza a página atual do Facebook
 */
async function refreshCurrentPage() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab && tab.url && tab.url.includes('facebook.com')) {
            await chrome.tabs.reload(tab.id);
            window.close();
        } else {
            // Se não estiver no Facebook, abre o Facebook
            await chrome.tabs.create({ url: 'https://www.facebook.com' });
            window.close();
        }
    } catch (error) {
        console.error('Erro ao atualizar página:', error);
    }
}

/**
 * Abre o Facebook em uma nova aba
 */
async function openFacebook() {
    try {
        await chrome.tabs.create({ url: 'https://www.facebook.com' });
        window.close();
    } catch (error) {
        console.error('Erro ao abrir Facebook:', error);
    }
}

/**
 * Event Listeners
 */
function setupEventListeners() {
    // Toggle principal da extensão
    elements.enableExtension.addEventListener('change', (e) => {
        currentConfig.isEnabled = e.target.checked;
        saveConfig();
        updateUI();
    });

    // Configurações individuais
    elements.hideStories.addEventListener('change', (e) => {
        currentConfig.hideStories = e.target.checked;
        saveConfig();
    });

    elements.hideReels.addEventListener('change', (e) => {
        currentConfig.hideReels = e.target.checked;
        saveConfig();
    });

    elements.hideSuggestionPhotos.addEventListener('change', (e) => {
        currentConfig.hideSuggestionPhotos = e.target.checked;
        saveConfig();
    });

    elements.enableLogging.addEventListener('change', (e) => {
        currentConfig.logEnabled = e.target.checked;
        saveConfig();
    });

    // Novas configurações
    elements.darkMode.addEventListener('change', (e) => {
        currentConfig.darkMode = e.target.checked;
        saveConfig();
        applyDarkMode();
    });

    elements.autoUpdate.addEventListener('change', (e) => {
        currentConfig.autoUpdate = e.target.checked;
        saveConfig();
    });

    // CSS personalizado
    elements.customCSS.addEventListener('blur', (e) => {
        currentConfig.customCSS = e.target.value;
        saveConfig();
    });

    // Whitelist
    elements.addToWhitelist.addEventListener('click', addToWhitelist);
    elements.whitelistInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addToWhitelist();
        }
    });

    // Toggle configurações avançadas
    elements.toggleAdvanced.addEventListener('click', toggleAdvancedSection);

    // Botões de ação
    elements.resetStats.addEventListener('click', resetStats);
    elements.refreshPage.addEventListener('click', refreshCurrentPage);
    elements.openFacebook.addEventListener('click', openFacebook);

    // Links do footer
    elements.reportIssue.addEventListener('click', (e) => {
        e.preventDefault();
        chrome.tabs.create({
            url: 'https://github.com/seu-usuario/facebook-cleaner/issues'
        });
    });

    elements.rateExtension.addEventListener('click', (e) => {
        e.preventDefault();
        chrome.tabs.create({
            url: 'https://chrome.google.com/webstore/detail/facebook-cleaner'
        });
    });
}

/**
 * Atualiza estatísticas em tempo real
 */
function startStatsUpdater() {
    // Atualiza estatísticas a cada 5 segundos
    setInterval(loadStats, 5000);

    // Listener para mudanças no storage
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local' && changes.stats) {
            currentStats = changes.stats.newValue || currentStats;
            updateStatsUI();
        }
    });
}

/**
 * Inicialização
 */
async function init() {
    console.log('Inicializando popup do Facebook Cleaner');

    await loadConfig();
    await loadStats();
    setupEventListeners();
    startStatsUpdater();

    console.log('Popup inicializado com sucesso');
}

// Inicializa quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', init);
