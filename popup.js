/**
 * Facebook Cleaner Extension - Popup Script
 * Gerencia a interface de configuração da extensão
 */

// Elementos do DOM
const elements = {
    enableExtension: document.getElementById('enableExtension'),
    hideStories: document.getElementById('hideStories'),
    hideReels: document.getElementById('hideReels'),
    hideSuggestionPhotos: document.getElementById('hideSuggestionPhotos'),
    enableLogging: document.getElementById('enableLogging'),
    statusDescription: document.getElementById('statusDescription'),
    storiesCount: document.getElementById('storiesCount'),
    reelsCount: document.getElementById('reelsCount'),
    photosCount: document.getElementById('photosCount'),
    resetStats: document.getElementById('resetStats'),
    refreshPage: document.getElementById('refreshPage'),
    openFacebook: document.getElementById('openFacebook'),
    reportIssue: document.getElementById('reportIssue'),
    rateExtension: document.getElementById('rateExtension')
};

// Estado da aplicação
let currentConfig = {
    isEnabled: true,
    hideStories: true,
    hideReels: true,
    hideSuggestionPhotos: true,
    logEnabled: false
};

let currentStats = {
    storiesOcultados: 0,
    reelsOcultados: 0,
    fotosRemovidas: 0
};

/**
 * Carrega configurações do storage
 */
async function loadConfig() {
    try {
        const result = await chrome.storage.sync.get([
            'isEnabled', 'hideStories', 'hideReels', 
            'hideSuggestionPhotos', 'logEnabled'
        ]);
        
        currentConfig = {
            isEnabled: result.isEnabled !== false,
            hideStories: result.hideStories !== false,
            hideReels: result.hideReels !== false,
            hideSuggestionPhotos: result.hideSuggestionPhotos !== false,
            logEnabled: result.logEnabled === true
        };
        
        updateUI();
    } catch (error) {
        console.error('Erro ao carregar configurações:', error);
    }
}

/**
 * Carrega estatísticas do storage
 */
async function loadStats() {
    try {
        const result = await chrome.storage.local.get(['stats']);
        if (result.stats) {
            currentStats = result.stats;
            updateStatsUI();
        }
    } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
    }
}

/**
 * Salva configurações no storage
 */
async function saveConfig() {
    try {
        await chrome.storage.sync.set(currentConfig);
        console.log('Configurações salvas');
    } catch (error) {
        console.error('Erro ao salvar configurações:', error);
    }
}

/**
 * Atualiza a interface com as configurações atuais
 */
function updateUI() {
    elements.enableExtension.checked = currentConfig.isEnabled;
    elements.hideStories.checked = currentConfig.hideStories;
    elements.hideReels.checked = currentConfig.hideReels;
    elements.hideSuggestionPhotos.checked = currentConfig.hideSuggestionPhotos;
    elements.enableLogging.checked = currentConfig.logEnabled;
    
    // Atualiza status
    const statusItem = document.querySelector('.status-item');
    if (currentConfig.isEnabled) {
        statusItem.classList.remove('disabled');
        elements.statusDescription.textContent = 'Ocultando conteúdo indesejado';
    } else {
        statusItem.classList.add('disabled');
        elements.statusDescription.textContent = 'Extensão desativada';
    }
    
    // Habilita/desabilita controles
    const configInputs = [elements.hideStories, elements.hideReels, elements.hideSuggestionPhotos];
    configInputs.forEach(input => {
        input.disabled = !currentConfig.isEnabled;
        input.parentElement.style.opacity = currentConfig.isEnabled ? '1' : '0.5';
    });
}

/**
 * Atualiza as estatísticas na interface
 */
function updateStatsUI() {
    elements.storiesCount.textContent = currentStats.storiesOcultados || 0;
    elements.reelsCount.textContent = currentStats.reelsOcultados || 0;
    elements.photosCount.textContent = currentStats.fotosRemovidas || 0;
}

/**
 * Reseta as estatísticas
 */
async function resetStats() {
    try {
        currentStats = {
            storiesOcultados: 0,
            reelsOcultados: 0,
            fotosRemovidas: 0
        };
        
        await chrome.storage.local.set({ stats: currentStats });
        updateStatsUI();
        
        // Feedback visual
        elements.resetStats.textContent = 'Resetado!';
        setTimeout(() => {
            elements.resetStats.textContent = 'Resetar Estatísticas';
        }, 1500);
    } catch (error) {
        console.error('Erro ao resetar estatísticas:', error);
    }
}

/**
 * Atualiza a página atual do Facebook
 */
async function refreshCurrentPage() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab && tab.url && tab.url.includes('facebook.com')) {
            await chrome.tabs.reload(tab.id);
            window.close();
        } else {
            // Se não estiver no Facebook, abre o Facebook
            await chrome.tabs.create({ url: 'https://www.facebook.com' });
            window.close();
        }
    } catch (error) {
        console.error('Erro ao atualizar página:', error);
    }
}

/**
 * Abre o Facebook em uma nova aba
 */
async function openFacebook() {
    try {
        await chrome.tabs.create({ url: 'https://www.facebook.com' });
        window.close();
    } catch (error) {
        console.error('Erro ao abrir Facebook:', error);
    }
}

/**
 * Event Listeners
 */
function setupEventListeners() {
    // Toggle principal da extensão
    elements.enableExtension.addEventListener('change', (e) => {
        currentConfig.isEnabled = e.target.checked;
        saveConfig();
        updateUI();
    });
    
    // Configurações individuais
    elements.hideStories.addEventListener('change', (e) => {
        currentConfig.hideStories = e.target.checked;
        saveConfig();
    });
    
    elements.hideReels.addEventListener('change', (e) => {
        currentConfig.hideReels = e.target.checked;
        saveConfig();
    });
    
    elements.hideSuggestionPhotos.addEventListener('change', (e) => {
        currentConfig.hideSuggestionPhotos = e.target.checked;
        saveConfig();
    });
    
    elements.enableLogging.addEventListener('change', (e) => {
        currentConfig.logEnabled = e.target.checked;
        saveConfig();
    });
    
    // Botões de ação
    elements.resetStats.addEventListener('click', resetStats);
    elements.refreshPage.addEventListener('click', refreshCurrentPage);
    elements.openFacebook.addEventListener('click', openFacebook);
    
    // Links do footer
    elements.reportIssue.addEventListener('click', (e) => {
        e.preventDefault();
        chrome.tabs.create({ 
            url: 'https://github.com/seu-usuario/facebook-cleaner/issues' 
        });
    });
    
    elements.rateExtension.addEventListener('click', (e) => {
        e.preventDefault();
        chrome.tabs.create({ 
            url: 'https://chrome.google.com/webstore/detail/facebook-cleaner' 
        });
    });
}

/**
 * Atualiza estatísticas em tempo real
 */
function startStatsUpdater() {
    // Atualiza estatísticas a cada 5 segundos
    setInterval(loadStats, 5000);
    
    // Listener para mudanças no storage
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local' && changes.stats) {
            currentStats = changes.stats.newValue || currentStats;
            updateStatsUI();
        }
    });
}

/**
 * Inicialização
 */
async function init() {
    console.log('Inicializando popup do Facebook Cleaner');
    
    await loadConfig();
    await loadStats();
    setupEventListeners();
    startStatsUpdater();
    
    console.log('Popup inicializado com sucesso');
}

// Inicializa quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', init);
