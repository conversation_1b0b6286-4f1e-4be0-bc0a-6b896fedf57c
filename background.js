/**
 * Facebook Cleaner Extension - Background Script
 * Gerencia funcionalidades em background, atualizações e configurações globais
 * Versão 1.1.0
 */

// Configurações padrão da extensão
const DEFAULT_CONFIG = {
    isEnabled: true,
    hideStories: true,
    hideReels: true,
    hideSuggestionPhotos: true,
    logEnabled: false,
    darkMode: false,
    whitelist: [],
    autoUpdate: true,
    version: '1.1.0'
};

// Estatísticas globais
let globalStats = {
    storiesOcultados: 0,
    reelsOcultados: 0,
    fotosRemovidas: 0,
    sessionsCount: 0,
    lastUsed: null,
    totalTimeActive: 0
};

/**
 * Inicialização da extensão
 */
chrome.runtime.onInstalled.addListener(async (details) => {
    console.log('Facebook Cleaner Extension instalada/atualizada');
    
    if (details.reason === 'install') {
        // Primeira instalação
        await initializeExtension();
        await showWelcomeNotification();
    } else if (details.reason === 'update') {
        // Atualização
        await handleUpdate(details.previousVersion);
    }
});

/**
 * Inicializa a extensão com configurações padrão
 */
async function initializeExtension() {
    try {
        // Define configurações padrão
        await chrome.storage.sync.set(DEFAULT_CONFIG);
        
        // Inicializa estatísticas
        await chrome.storage.local.set({ 
            stats: globalStats,
            installDate: new Date().toISOString()
        });
        
        console.log('Extensão inicializada com configurações padrão');
    } catch (error) {
        console.error('Erro ao inicializar extensão:', error);
    }
}

/**
 * Mostra notificação de boas-vindas
 */
async function showWelcomeNotification() {
    try {
        await chrome.notifications.create('welcome', {
            type: 'basic',
            iconUrl: 'icons/icon128.png',
            title: 'Facebook Cleaner Instalado!',
            message: 'Clique no ícone da extensão para configurar suas preferências.'
        });
    } catch (error) {
        console.error('Erro ao mostrar notificação:', error);
    }
}

/**
 * Gerencia atualizações da extensão
 */
async function handleUpdate(previousVersion) {
    try {
        console.log(`Atualizando de ${previousVersion} para ${DEFAULT_CONFIG.version}`);
        
        // Migra configurações se necessário
        await migrateSettings(previousVersion);
        
        // Atualiza versão
        await chrome.storage.sync.set({ version: DEFAULT_CONFIG.version });
        
        // Mostra notificação de atualização
        await chrome.notifications.create('update', {
            type: 'basic',
            iconUrl: 'icons/icon128.png',
            title: 'Facebook Cleaner Atualizado!',
            message: `Nova versão ${DEFAULT_CONFIG.version} instalada com melhorias.`
        });
    } catch (error) {
        console.error('Erro ao processar atualização:', error);
    }
}

/**
 * Migra configurações de versões anteriores
 */
async function migrateSettings(previousVersion) {
    try {
        const currentSettings = await chrome.storage.sync.get();
        
        // Adiciona novas configurações se não existirem
        const updatedSettings = { ...DEFAULT_CONFIG, ...currentSettings };
        
        await chrome.storage.sync.set(updatedSettings);
        console.log('Configurações migradas com sucesso');
    } catch (error) {
        console.error('Erro ao migrar configurações:', error);
    }
}

/**
 * Listener para cliques no ícone da extensão
 */
chrome.action.onClicked.addListener(async (tab) => {
    // Se estiver no Facebook, abre o popup
    if (tab.url && tab.url.includes('facebook.com')) {
        // O popup abrirá automaticamente
        return;
    }
    
    // Se não estiver no Facebook, abre o Facebook
    await chrome.tabs.create({ url: 'https://www.facebook.com' });
});

/**
 * Listener para mudanças nas abas
 */
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('facebook.com')) {
        // Incrementa contador de sessões
        await incrementSessionCount();
        
        // Verifica se há atualizações de seletores CSS
        await checkForSelectorUpdates();
    }
});

/**
 * Incrementa contador de sessões
 */
async function incrementSessionCount() {
    try {
        const result = await chrome.storage.local.get(['stats']);
        const stats = result.stats || globalStats;
        
        stats.sessionsCount = (stats.sessionsCount || 0) + 1;
        stats.lastUsed = new Date().toISOString();
        
        await chrome.storage.local.set({ stats });
    } catch (error) {
        console.error('Erro ao incrementar contador de sessões:', error);
    }
}

/**
 * Verifica atualizações de seletores CSS
 */
async function checkForSelectorUpdates() {
    try {
        const config = await chrome.storage.sync.get(['autoUpdate', 'lastSelectorUpdate']);
        
        if (!config.autoUpdate) return;
        
        const lastUpdate = config.lastSelectorUpdate ? new Date(config.lastSelectorUpdate) : new Date(0);
        const now = new Date();
        const daysSinceUpdate = (now - lastUpdate) / (1000 * 60 * 60 * 24);
        
        // Verifica atualizações a cada 7 dias
        if (daysSinceUpdate >= 7) {
            await updateCSSSelectors();
        }
    } catch (error) {
        console.error('Erro ao verificar atualizações de seletores:', error);
    }
}

/**
 * Atualiza seletores CSS dinamicamente
 */
async function updateCSSSelectors() {
    try {
        // Aqui você pode implementar lógica para buscar seletores atualizados
        // de um servidor ou API
        
        console.log('Verificando atualizações de seletores CSS...');
        
        // Por enquanto, apenas atualiza a data da última verificação
        await chrome.storage.sync.set({ 
            lastSelectorUpdate: new Date().toISOString() 
        });
        
        console.log('Verificação de seletores concluída');
    } catch (error) {
        console.error('Erro ao atualizar seletores:', error);
    }
}

/**
 * Listener para comandos de teclado
 */
chrome.commands.onCommand.addListener(async (command) => {
    switch (command) {
        case 'toggle-extension':
            await toggleExtension();
            break;
        case 'toggle-stories':
            await toggleFeature('hideStories');
            break;
        case 'toggle-reels':
            await toggleFeature('hideReels');
            break;
    }
});

/**
 * Alterna estado da extensão
 */
async function toggleExtension() {
    try {
        const config = await chrome.storage.sync.get(['isEnabled']);
        const newState = !config.isEnabled;
        
        await chrome.storage.sync.set({ isEnabled: newState });
        
        // Mostra notificação
        await chrome.notifications.create('toggle', {
            type: 'basic',
            iconUrl: 'icons/icon128.png',
            title: 'Facebook Cleaner',
            message: `Extensão ${newState ? 'ativada' : 'desativada'}`
        });
    } catch (error) {
        console.error('Erro ao alternar extensão:', error);
    }
}

/**
 * Alterna funcionalidade específica
 */
async function toggleFeature(feature) {
    try {
        const config = await chrome.storage.sync.get([feature]);
        const newState = !config[feature];
        
        await chrome.storage.sync.set({ [feature]: newState });
        
        const featureNames = {
            hideStories: 'Stories',
            hideReels: 'Reels',
            hideSuggestionPhotos: 'Fotos de Sugestões'
        };
        
        await chrome.notifications.create('feature-toggle', {
            type: 'basic',
            iconUrl: 'icons/icon128.png',
            title: 'Facebook Cleaner',
            message: `${featureNames[feature]} ${newState ? 'ativado' : 'desativado'}`
        });
    } catch (error) {
        console.error('Erro ao alternar funcionalidade:', error);
    }
}

/**
 * Listener para mensagens dos content scripts
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
        case 'updateStats':
            updateGlobalStats(request.stats);
            break;
        case 'getConfig':
            getConfiguration().then(sendResponse);
            return true; // Indica resposta assíncrona
        case 'reportError':
            logError(request.error, sender.tab);
            break;
    }
});

/**
 * Atualiza estatísticas globais
 */
async function updateGlobalStats(newStats) {
    try {
        const result = await chrome.storage.local.get(['stats']);
        const currentStats = result.stats || globalStats;
        
        // Merge das estatísticas
        const updatedStats = {
            ...currentStats,
            storiesOcultados: (currentStats.storiesOcultados || 0) + (newStats.storiesOcultados || 0),
            reelsOcultados: (currentStats.reelsOcultados || 0) + (newStats.reelsOcultados || 0),
            fotosRemovidas: (currentStats.fotosRemovidas || 0) + (newStats.fotosRemovidas || 0),
            lastUsed: new Date().toISOString()
        };
        
        await chrome.storage.local.set({ stats: updatedStats });
    } catch (error) {
        console.error('Erro ao atualizar estatísticas:', error);
    }
}

/**
 * Obtém configuração atual
 */
async function getConfiguration() {
    try {
        const config = await chrome.storage.sync.get();
        return { ...DEFAULT_CONFIG, ...config };
    } catch (error) {
        console.error('Erro ao obter configuração:', error);
        return DEFAULT_CONFIG;
    }
}

/**
 * Registra erros para análise
 */
function logError(error, tab) {
    console.error('Erro reportado pelo content script:', {
        error,
        url: tab?.url,
        timestamp: new Date().toISOString()
    });
}

console.log('Facebook Cleaner Background Script carregado');
